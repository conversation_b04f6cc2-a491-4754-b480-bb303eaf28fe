'use client'

import type { BaseLayoutProps } from './layouts/types'
import { cn } from '@/libs/utils'
import React from 'react'
import {
  BannerSection,
  BookingFormSection,
  BookingInfoSection,
  ContactSection,
  DescriptionSection,
  GridSlotSection,
  MapSection,
} from './layouts'

interface ClassicSportTemplateProps extends BaseLayoutProps {
  selectedDate?: Date
  onDateChange?: (date: Date) => void
  onSlotSelect?: (fieldId: string, timeSlot: string) => void
  onBookingSubmit?: (data: {
    customerName: string
    customerEmail: string
    customerPhone: string
    notes?: string
  }) => void
}

/**
 * Classic Sport Template Component
 *
 * Layout structure - Traditional vertical layout:
 * -----------------
 * |Banner image   |
 * -----------------
 * |description    |
 * -----------------
 * |booking info   |
 * -----------------
 * |grid slot      |
 * -----------------
 * |contact        |
 * -----------------
 * |map            |
 * -----------------
 */
const ClassicSportTemplate: React.FC<ClassicSportTemplateProps> = React.memo(({
  config,
  pageInfo,
  previewMode = 'desktop',
  className,
  selectedDate,
  onDateChange,
  onSlotSelect,
  onBookingSubmit,
}) => {
  const isMobile = previewMode === 'mobile'

  return (
    <div className={cn(
      'w-full space-y-6',
      isMobile ? 'p-4' : 'p-6',
      className,
    )}
    >
      {/* Banner Section */}
      <BannerSection
        config={config}
        pageInfo={pageInfo}
        previewMode={previewMode}
      />

      {/* Description Section */}
      <DescriptionSection
        config={config}
        pageInfo={pageInfo}
        previewMode={previewMode}
      />

      {/* Booking Info Section */}
      <BookingInfoSection
        config={config}
        pageInfo={pageInfo}
        previewMode={previewMode}
        showPricing={config.pricing?.showPricing ?? true}
        showCapacity={config.showCapacity ?? true}
        showFieldTypes={config.showFieldTypes ?? true}
      />

      {/* Grid Slot Section */}
      <GridSlotSection
        config={config}
        pageInfo={pageInfo}
        previewMode={previewMode}
        selectedDate={selectedDate}
        onDateChange={onDateChange}
        onSlotSelect={onSlotSelect}
      />

      {/* Booking Form Section */}
      <BookingFormSection
        config={config}
        pageInfo={pageInfo}
        previewMode={previewMode}
        onSubmit={onBookingSubmit}
        showNotes={true}
      />

      {/* Contact Section */}
      <ContactSection
        config={config}
        pageInfo={pageInfo}
        previewMode={previewMode}
      />

      {/* Map Section */}
      <MapSection
        config={config}
        pageInfo={pageInfo}
        previewMode={previewMode}
        showDirections={config.showDirections ?? true}
      />
    </div>
  )
})

export default ClassicSportTemplate
