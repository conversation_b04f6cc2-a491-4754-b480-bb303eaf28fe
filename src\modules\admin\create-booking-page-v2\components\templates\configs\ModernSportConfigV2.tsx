'use client'

import { Settings } from 'lucide-react'
import React, { useCallback } from 'react'
import { useCreateBookingPageV2Store } from '../../../stores/create-booking-page-v2.store'
import {
  BasicInfoConfig,
  ContactInfoConfig,
  DescriptionLocationConfig,
  FieldsConfig,
  OperatingHoursConfig,
} from './shared'

const ModernSportConfig: React.FC = React.memo(() => {
  // Use direct store access
  const config = useCreateBookingPageV2Store(state => state.bookingConfig)
  const updateBookingConfig = useCreateBookingPageV2Store(state => state.updateBookingConfig)
  const addField = useCreateBookingPageV2Store(state => state.addField)
  const removeField = useCreateBookingPageV2Store(state => state.removeField)
  const updateField = useCreateBookingPageV2Store(state => state.updateField)

  // Memoized handlers for config updates
  const handleBasicInfoUpdate = useCallback((updates: any) => {
    updateBookingConfig(updates)
  }, [updateBookingConfig])

  const handleOperatingHoursUpdate = useCallback((updates: any) => {
    updateBookingConfig(updates)
  }, [updateBookingConfig])

  const handleDescriptionLocationUpdate = useCallback((updates: any) => {
    updateBookingConfig(updates)
  }, [updateBookingConfig])

  const handleContactInfoUpdate = useCallback((updates: any) => {
    updateBookingConfig(updates)
  }, [updateBookingConfig])

  // Memoized handlers for fields
  const handleFieldAdd = useCallback(() => {
    addField()
  }, [addField])

  const handleFieldRemove = useCallback((fieldId: string) => {
    removeField(fieldId)
  }, [removeField])

  const handleFieldUpdate = useCallback((fieldId: string, updates: any) => {
    updateField(fieldId, updates)
  }, [updateField])

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Settings className="w-6 h-6 text-orange-500" />
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            Cấu hình sân thể thao
          </h3>
          <p className="text-sm text-gray-600">
            Thiết lập thông tin cơ bản cho trang đặt sân
          </p>
        </div>
      </div>

      {/* Basic Information */}
      <BasicInfoConfig
        config={{
          bannerTitle: config.bannerTitle,
          bannerSubtitle: config.bannerSubtitle,
          bannerImage: config.bannerImage,
          bannerBackgroundColor: config.bannerBackgroundColor,
          bannerImageFit: config.bannerImageFit,
        }}
        onUpdate={handleBasicInfoUpdate}
      />

      {/* Operating Hours */}
      <OperatingHoursConfig
        config={{
          openTime: config.openTime,
          closeTime: config.closeTime,
        }}
        onUpdate={handleOperatingHoursUpdate}
      />

      {/* Fields Configuration */}
      <FieldsConfig
        config={{
          fields: config.fields,
        }}
        onAddField={handleFieldAdd}
        onRemoveField={handleFieldRemove}
        onUpdateField={handleFieldUpdate}
      />

      {/* Description & Location */}
      <DescriptionLocationConfig
        config={{
          description: config.description,
          location: config.location,
        }}
        onUpdate={handleDescriptionLocationUpdate}
      />

      {/* Contact Information */}
      <ContactInfoConfig
        config={{
          contactInfo: config.contactInfo,
        }}
        onUpdate={handleContactInfoUpdate}
      />
    </div>
  )
})

ModernSportConfig.displayName = 'ModernSportConfig'

export default ModernSportConfig
