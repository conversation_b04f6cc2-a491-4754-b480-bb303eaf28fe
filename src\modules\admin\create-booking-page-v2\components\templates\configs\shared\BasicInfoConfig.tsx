'use client'

import React, { useCallback, useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Image as ImageIcon, Palette, Settings } from 'lucide-react'
import { useDebounce } from '../hooks/useDebounce'

interface BasicInfoConfigProps {
  config: {
    bannerTitle: string
    bannerSubtitle: string
    bannerImage?: string
    bannerBackgroundColor?: string
    bannerImageFit?: 'cover' | 'contain'
  }
  onUpdate: (updates: Partial<BasicInfoConfigProps['config']>) => void
}

export const BasicInfoConfig: React.FC<BasicInfoConfigProps> = React.memo(({ config, onUpdate }) => {
  // Local state for immediate UI updates
  const [localBannerTitle, setLocalBannerTitle] = useState(config.bannerTitle)
  const [localBannerSubtitle, setLocalBannerSubtitle] = useState(config.bannerSubtitle)
  const [localBannerBackgroundColor, setLocalBannerBackgroundColor] = useState(config.bannerBackgroundColor || '')
  const [localBannerImageFit, setLocalBannerImageFit] = useState<'cover' | 'contain'>(
    (config.bannerImageFit === 'cover' || config.bannerImageFit === 'contain')
      ? config.bannerImageFit
      : 'cover',
  )

  // Debounced values
  const debouncedBannerTitle = useDebounce(localBannerTitle, 300)
  const debouncedBannerSubtitle = useDebounce(localBannerSubtitle, 300)
  const debouncedBannerBackgroundColor = useDebounce(localBannerBackgroundColor, 300)
  const debouncedBannerImageFit = useDebounce(localBannerImageFit, 300)

  // Update config when debounced values change
  useEffect(() => {
    if (debouncedBannerTitle !== config.bannerTitle) {
      onUpdate({ bannerTitle: debouncedBannerTitle })
    }
  }, [debouncedBannerTitle, config.bannerTitle, onUpdate])

  useEffect(() => {
    if (debouncedBannerSubtitle !== config.bannerSubtitle) {
      onUpdate({ bannerSubtitle: debouncedBannerSubtitle })
    }
  }, [debouncedBannerSubtitle, config.bannerSubtitle, onUpdate])

  useEffect(() => {
    if (debouncedBannerBackgroundColor !== config.bannerBackgroundColor) {
      onUpdate({ bannerBackgroundColor: debouncedBannerBackgroundColor })
    }
  }, [debouncedBannerBackgroundColor, config.bannerBackgroundColor, onUpdate])

  useEffect(() => {
    if (debouncedBannerImageFit !== config.bannerImageFit) {
      onUpdate({ bannerImageFit: debouncedBannerImageFit as any })
    }
  }, [debouncedBannerImageFit, config.bannerImageFit, onUpdate])

  const handleImageUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // In real app, upload to server and get URL
      const imageUrl = URL.createObjectURL(file)
      onUpdate({ bannerImage: imageUrl })
    }
  }, [onUpdate])

  const handleRemoveBannerImage = useCallback(() => {
    onUpdate({ bannerImage: '' })
  }, [onUpdate])

  const handleRemoveBackgroundColor = useCallback(() => {
    setLocalBannerBackgroundColor('')
    onUpdate({ bannerBackgroundColor: '' })
  }, [onUpdate])

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base flex items-center gap-2">
          <Settings className="w-4 h-4" />
          Thông tin cơ bản
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="banner-title">Tiêu đề banner</Label>
          <Input
            id="banner-title"
            value={localBannerTitle}
            onChange={e => setLocalBannerTitle(e.target.value)}
            placeholder="VD: CLB Cầu lông"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="banner-subtitle">Mô tả ngắn</Label>
          <Input
            id="banner-subtitle"
            value={localBannerSubtitle}
            onChange={e => setLocalBannerSubtitle(e.target.value)}
            placeholder="VD: Đặt sân cầu lông chất lượng cao"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="banner-image">Ảnh banner</Label>
          <div className="flex items-center gap-3">
            <Input
              id="banner-image"
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
            />
            <Button
              type="button"
              variant="outline"
              onClick={() => document.getElementById('banner-image')?.click()}
              className="flex items-center gap-2"
            >
              <ImageIcon className="w-4 h-4" />
              Chọn ảnh
            </Button>
            {config.bannerImage && (
              <div className="flex items-center gap-2">
                <img
                  src={config.bannerImage}
                  alt="Banner preview"
                  className="w-16 h-10 object-cover rounded border"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleRemoveBannerImage}
                >
                  Xóa
                </Button>
              </div>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="banner-background-color" className="flex items-center gap-2">
            <Palette className="w-4 h-4" />
            Màu nền banner
          </Label>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <Input
                id="banner-background-color"
                type="color"
                value={localBannerBackgroundColor}
                onChange={e => setLocalBannerBackgroundColor(e.target.value)}
                className="w-16 h-10 p-1 border rounded cursor-pointer"
              />
              <Input
                type="text"
                value={localBannerBackgroundColor}
                onChange={e => setLocalBannerBackgroundColor(e.target.value)}
                placeholder="#3B82F6"
                className="flex-1"
              />
            </div>
            {localBannerBackgroundColor && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={handleRemoveBackgroundColor}
              >
                Xóa
              </Button>
            )}
          </div>
          <p className="text-sm text-gray-600">
            Chọn màu nền cho banner. Nếu có màu nền, sẽ ưu tiên hiển thị màu thay vì ảnh.
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="banner-image-fit">Cách hiển thị ảnh</Label>
          <Select
            value={localBannerImageFit}
            onValueChange={(value: 'cover' | 'contain') => setLocalBannerImageFit(value)}
          >
            <SelectTrigger className="w-full">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="cover">
                <div className="flex items-center gap-2">
                  <span>🖼️</span>
                  <span>Cover - Phủ toàn bộ</span>
                </div>
              </SelectItem>
              <SelectItem value="contain">
                <div className="flex items-center gap-2">
                  <span>📐</span>
                  <span>Contain - Hiển thị đầy đủ</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
          <p className="text-sm text-gray-600">
            Cover: Ảnh sẽ phủ toàn bộ banner (có thể bị cắt). Contain: Ảnh hiển thị đầy đủ (có thể có khoảng trống).
          </p>
        </div>
      </CardContent>
    </Card>
  )
})

BasicInfoConfig.displayName = 'BasicInfoConfig'
