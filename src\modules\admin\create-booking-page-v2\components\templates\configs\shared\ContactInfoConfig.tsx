'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Phone } from 'lucide-react'
import React from 'react'

interface ContactInfoConfigProps {
  config: {
    contactInfo: {
      phone: string
      email: string
      address: string
      socialLinks: {
        facebook?: string
        instagram?: string
        website?: string
      }
    }
  }
  onUpdate: (updates: Partial<ContactInfoConfigProps['config']>) => void
}

export const ContactInfoConfig: React.FC<ContactInfoConfigProps> = React.memo(({ config, onUpdate }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base flex items-center gap-2">
          <Phone className="w-4 h-4" />
          Thông tin liên hệ
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="contact-phone">Số điện thoại</Label>
            <Input
              id="contact-phone"
              value={config.contactInfo.phone}
              onChange={e => onUpdate({
                contactInfo: { ...config.contactInfo, phone: e.target.value },
              })}
              placeholder="VD: 0949 029 965"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="contact-email">Email</Label>
            <Input
              id="contact-email"
              type="email"
              value={config.contactInfo.email}
              onChange={e => onUpdate({
                contactInfo: { ...config.contactInfo, email: e.target.value },
              })}
              placeholder="VD: <EMAIL>"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="contact-address">Địa chỉ</Label>
          <Input
            id="contact-address"
            value={config.contactInfo.address}
            onChange={e => onUpdate({
              contactInfo: { ...config.contactInfo, address: e.target.value },
            })}
            placeholder="VD: Số 40, đường số 11, phường Trường Thọ, TP.Thủ Đức"
          />
        </div>

        <Separator />

        <div className="space-y-4">
          <Label className="text-sm font-medium">Mạng xã hội (tùy chọn)</Label>

          <div className="grid grid-cols-1 gap-4">
            <div className="space-y-2">
              <Label htmlFor="facebook">Facebook</Label>
              <Input
                id="facebook"
                value={config.contactInfo.socialLinks.facebook || ''}
                onChange={e => onUpdate({
                  contactInfo: {
                    ...config.contactInfo,
                    socialLinks: { ...config.contactInfo.socialLinks, facebook: e.target.value },
                  },
                })}
                placeholder="https://facebook.com/..."
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="instagram">Instagram</Label>
              <Input
                id="instagram"
                value={config.contactInfo.socialLinks.instagram || ''}
                onChange={e => onUpdate({
                  contactInfo: {
                    ...config.contactInfo,
                    socialLinks: { ...config.contactInfo.socialLinks, instagram: e.target.value },
                  },
                })}
                placeholder="https://instagram.com/..."
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="website">Website</Label>
              <Input
                id="website"
                value={config.contactInfo.socialLinks.website || ''}
                onChange={e => onUpdate({
                  contactInfo: {
                    ...config.contactInfo,
                    socialLinks: { ...config.contactInfo.socialLinks, website: e.target.value },
                  },
                })}
                placeholder="https://example.com"
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
})

ContactInfoConfig.displayName = 'ContactInfoConfig'
