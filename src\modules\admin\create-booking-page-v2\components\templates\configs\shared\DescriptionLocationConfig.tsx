'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ead<PERSON>, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Settings } from 'lucide-react'
import React from 'react'

interface DescriptionLocationConfigProps {
  config: {
    description: string
    location: string
  }
  onUpdate: (updates: Partial<DescriptionLocationConfigProps['config']>) => void
}

export const DescriptionLocationConfig: React.FC<DescriptionLocationConfigProps> = React.memo(({ config, onUpdate }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base flex items-center gap-2">
          <Settings className="w-4 h-4" />
          Mô tả & Địa điểm
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="description"><PERSON><PERSON> tả chi tiết</Label>
          <Textarea
            id="description"
            value={config.description}
            onChange={e => onUpdate({ description: e.target.value })}
            placeholder="VD: Sân thể thao hiện đại với đầy đủ tiện nghi..."
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="location">Địa điểm</Label>
          <Input
            id="location"
            value={config.location}
            onChange={e => onUpdate({ location: e.target.value })}
            placeholder="VD: Số 123, Đường ABC, Quận 1, TP.HCM"
          />
        </div>
      </CardContent>
    </Card>
  )
})

DescriptionLocationConfig.displayName = 'DescriptionLocationConfig'
