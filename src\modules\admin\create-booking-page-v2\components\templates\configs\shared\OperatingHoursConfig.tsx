'use client'

import React, { useCallback } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Clock } from 'lucide-react'

interface OperatingHoursConfigProps {
  config: {
    openTime: string
    closeTime: string
  }
  onUpdate: (updates: Partial<OperatingHoursConfigProps['config']>) => void
}

export const OperatingHoursConfig: React.FC<OperatingHoursConfigProps> = React.memo(({ config, onUpdate }) => {
  const handleOpenTimeChange = useCallback((value: string) => {
    onUpdate({ openTime: value })
  }, [onUpdate])

  const handleCloseTimeChange = useCallback((value: string) => {
    onUpdate({ closeTime: value })
  }, [onUpdate])

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base flex items-center gap-2">
          <Clock className="w-4 h-4" />
          Gi<PERSON> hoạt động
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="open-time">Giờ mở cửa</Label>
            <Input
              id="open-time"
              type="time"
              value={config.openTime}
              onChange={e => handleOpenTimeChange(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="close-time">Giờ đóng cửa</Label>
            <Input
              id="close-time"
              type="time"
              value={config.closeTime}
              onChange={e => handleCloseTimeChange(e.target.value)}
            />
          </div>
        </div>
        <p className="text-sm text-gray-600">
          Lịch đặt sân sẽ hiển thị các khung giờ từ
          {' '}
          {config.openTime}
          {' '}
          đến
          {' '}
          {config.closeTime}
        </p>
      </CardContent>
    </Card>
  )
})

OperatingHoursConfig.displayName = 'OperatingHoursConfig'
