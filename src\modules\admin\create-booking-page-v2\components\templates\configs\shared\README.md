# Shared Config Components

<PERSON><PERSON><PERSON> mục này chứa các config components có thể tái sử dụng cho các template khác nhau.

## Cấu trúc

```
shared/
├── BasicInfoConfig.tsx          # C<PERSON>u hình thông tin cơ bản (banner, title, subtitle, image, colors)
├── OperatingHoursConfig.tsx     # Cấu hình giờ hoạt động
├── FieldsConfig.tsx             # Cấu hình danh sách sân
├── DescriptionLocationConfig.tsx # Cấu hình mô tả và địa điểm
├── ContactInfoConfig.tsx        # Cấu hình thông tin liên hệ
├── index.ts                     # Export tất cả components
└── README.md                    # Tài liệu này
```

## Cách sử dụng

### 1. Import các components

```tsx
import { 
  BasicInfoConfig, 
  OperatingHoursConfig, 
  FieldsConfig, 
  DescriptionLocationConfig, 
  ContactInfoConfig 
} from './shared'
```

### 2. Sử dụng trong template config

```tsx
const YourTemplateConfig: React.FC = React.memo(() => {
  const config = useCreateBookingPageV2Store(state => state.bookingConfig)
  const updateBookingConfig = useCreateBookingPageV2Store(state => state.updateBookingConfig)
  const addField = useCreateBookingPageV2Store(state => state.addField)
  const removeField = useCreateBookingPageV2Store(state => state.removeField)
  const updateField = useCreateBookingPageV2Store(state => state.updateField)

  // Handlers
  const handleBasicInfoUpdate = useCallback((updates: any) => {
    updateBookingConfig(updates)
  }, [updateBookingConfig])

  const handleFieldAdd = useCallback(() => {
    addField()
  }, [addField])

  // ... other handlers

  return (
    <div className="space-y-6">
      <BasicInfoConfig
        config={{
          bannerTitle: config.bannerTitle,
          bannerSubtitle: config.bannerSubtitle,
          bannerImage: config.bannerImage,
          bannerBackgroundColor: config.bannerBackgroundColor,
          bannerImageFit: config.bannerImageFit,
        }}
        onUpdate={handleBasicInfoUpdate}
      />
      
      <OperatingHoursConfig
        config={{
          openTime: config.openTime,
          closeTime: config.closeTime,
        }}
        onUpdate={handleOperatingHoursUpdate}
      />
      
      {/* ... other components */}
    </div>
  )
})
```

## Components chi tiết

### BasicInfoConfig
- **Props**: `config` (banner info), `onUpdate` (callback)
- **Chức năng**: Cấu hình tiêu đề, mô tả, ảnh banner, màu nền, cách hiển thị ảnh
- **Features**: Debounced input, image upload, color picker

### OperatingHoursConfig
- **Props**: `config` (open/close time), `onUpdate` (callback)
- **Chức năng**: Cấu hình giờ mở cửa và đóng cửa

### FieldsConfig
- **Props**: `config` (fields array), `onAddField`, `onRemoveField`, `onUpdateField`
- **Chức năng**: Quản lý danh sách sân (thêm, xóa, sửa)
- **Features**: Debounced field name input, field type selection

### DescriptionLocationConfig
- **Props**: `config` (description, location), `onUpdate` (callback)
- **Chức năng**: Cấu hình mô tả chi tiết và địa điểm

### ContactInfoConfig
- **Props**: `config` (contactInfo object), `onUpdate` (callback)
- **Chức năng**: Cấu hình thông tin liên hệ và mạng xã hội

## Lợi ích

1. **Tái sử dụng code**: Các config blocks có thể dùng cho nhiều template
2. **Dễ bảo trì**: Chỉ cần sửa ở một nơi, tất cả template đều được cập nhật
3. **Consistent UI**: Đảm bảo giao diện nhất quán giữa các template
4. **Performance**: Sử dụng React.memo và debounced input để tối ưu hiệu suất
5. **Type Safety**: Có type definitions rõ ràng cho props

## Hooks hỗ trợ

### useDebounce
- **Location**: `../hooks/useDebounce.ts`
- **Chức năng**: Debounce input để tránh re-render quá nhiều
- **Usage**: `const debouncedValue = useDebounce(value, 300)`

## Mở rộng

Để thêm config component mới:

1. Tạo file component trong thư mục `shared/`
2. Export component trong `index.ts`
3. Sử dụng trong các template config
4. Cập nhật README này

## Ví dụ template hoàn chỉnh

Xem `ModernSportConfigV2.tsx` và `ClassicSportConfig.tsx` để biết cách sử dụng đầy đủ các shared components.
