/* eslint-disable no-unused-vars */
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

// Types
export interface SelectedSlotsState {
  selectedSlots: Record<string, string[]>
  selectedDate: Date

  // Actions
  setSelectedSlots: (fieldId: string, timeSlots: string[]) => void
  addSlot: (fieldId: string, timeSlot: string) => void
  removeSlot: (fieldId: string, timeSlot: string) => void
  toggleSlot: (fieldId: string, timeSlot: string) => void
  clearFieldSlots: (fieldId: string) => void
  clearAllSlots: () => void
  setSelectedDate: (date: Date) => void

  // Getters
  getFieldSlots: (fieldId: string) => string[]
  hasSelectedSlots: () => boolean
  getTotalSelectedSlots: () => number
}

// Store
export const useSelectedSlotsStore = create<SelectedSlotsState>()(
  devtools(
    (set, get) => ({
      // State
      selectedSlots: {},
      selectedDate: new Date(),

      // Actions
      setSelectedSlots: (fieldId: string, timeSlots: string[]) => {
        set(
          state => ({
            selectedSlots: {
              ...state.selectedSlots,
              [fieldId]: timeSlots,
            },
          }),
          false,
          'setSelectedSlots',
        )
      },

      addSlot: (fieldId: string, timeSlot: string) => {
        set(
          (state) => {
            const currentSlots = state.selectedSlots[fieldId] || []
            if (!currentSlots.includes(timeSlot)) {
              return {
                selectedSlots: {
                  ...state.selectedSlots,
                  [fieldId]: [...currentSlots, timeSlot],
                },
              }
            }
            return state
          },
          false,
          'addSlot',
        )
      },

      removeSlot: (fieldId: string, timeSlot: string) => {
        set(
          (state) => {
            const currentSlots = state.selectedSlots[fieldId] || []
            const updatedSlots = currentSlots.filter(slot => slot !== timeSlot)

            if (updatedSlots.length === 0) {
              // Remove field if no slots left
              const { [fieldId]: removed, ...remainingSlots } = state.selectedSlots
              return { selectedSlots: remainingSlots }
            }

            return {
              selectedSlots: {
                ...state.selectedSlots,
                [fieldId]: updatedSlots,
              },
            }
          },
          false,
          'removeSlot',
        )
      },

      toggleSlot: (fieldId: string, timeSlot: string) => {
        const { selectedSlots } = get()
        const currentSlots = selectedSlots[fieldId] || []
        const isSelected = currentSlots.includes(timeSlot)

        if (isSelected) {
          get().removeSlot(fieldId, timeSlot)
        } else {
          get().addSlot(fieldId, timeSlot)
        }
      },

      clearFieldSlots: (fieldId: string) => {
        set(
          (state) => {
            const { [fieldId]: removed, ...remainingSlots } = state.selectedSlots
            return { selectedSlots: remainingSlots }
          },
          false,
          'clearFieldSlots',
        )
      },

      clearAllSlots: () => {
        set({ selectedSlots: {} }, false, 'clearAllSlots')
      },

      setSelectedDate: (date: Date) => {
        set({ selectedDate: date }, false, 'setSelectedDate')
      },

      // Getters
      getFieldSlots: (fieldId: string) => {
        return get().selectedSlots[fieldId] || []
      },

      hasSelectedSlots: () => {
        return Object.keys(get().selectedSlots).length > 0
      },

      getTotalSelectedSlots: () => {
        const { selectedSlots } = get()
        return Object.values(selectedSlots).reduce((total, slots) => total + slots.length, 0)
      },
    }),
    {
      name: 'selected-slots-store',
    },
  ),
)
